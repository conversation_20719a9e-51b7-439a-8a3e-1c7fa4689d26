# TradeSync - Technical Design Document

## Overview

This document provides detailed technical specifications for the TradeSync multi-account social trading platform, including system architecture, API design, database schemas, and implementation details for users managing multiple trading accounts with dual provider/follower capabilities.

## Clarified System Requirements

### User Account Structure
- **Single User Login**: One user authentication per person
- **Multiple Trading Accounts**: Each user can connect multiple trading accounts from different brokers
- **Dual-Role Capability**: Each trading account can simultaneously:
  - Act as a **Signal Provider** (broadcasting trades to followers)
  - Act as a **Follower** (copying trades from multiple providers with individual risk settings)

### Key Features
- **Multi-Provider Copying**: Each account can copy from multiple signal providers simultaneously
- **Granular Risk Management**: Individual risk settings per copy relationship
- **Cross-Broker Support**: Accounts from different brokers can interact
- **Real-time Signal Processing**: Sub-3-second trade copying latency

## System Architecture

### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Frontend  │    │   Mobile App    │    │  Desktop App    │
│    (React)      │    │   (Optional)    │    │   (Optional)    │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                     ┌───────────▼───────────┐
                     │   Load Balancer       │
                     │   (nginx/AWS ALB)     │
                     └───────────┬───────────┘
                                 │
                ┌────────────────┼────────────────┐
                │                │                │
        ┌───────▼───────┐   ┌────▼────┐   ┌──────▼──────┐
        │ Backend API   │   │WebSocket│   │Copy Trading │
        │    Server     │   │ Server  │   │   Engine    │
        │(Python/FastAPI│   │(FastAPI)│   │  (Python)   │
        └───────┬───────┘   └────┬────┘   └──────┬──────┘
                │               │                │
                └───────────────┼────────────────┘
                                │
                ┌───────────────▼───────────────┐
                │        Message Queue          │
                │     (Redis Pub/Sub)          │
                └───────────────┬───────────────┘
                                │
        ┌───────────────────────┼───────────────────────┐
        │                       │                       │
┌───────▼───────┐      ┌────────▼────────┐      ┌───────▼───────┐
│   PostgreSQL   │      │   Trading       │      │    Redis      │
│   Database     │      │ Connectors      │      │    Cache      │
│                │      │   Layer         │      │               │
└────────────────┘      └────────┬────────┘      └───────────────┘
                                 │
        ┌────────────────────────┼────────────────────────┐
        │                        │                        │
  ┌─────▼──────┐        ┌────────▼────────┐        ┌─────▼──────┐
  │   Broker   │        │    MT5 Python   │        │ MT4 Bridge │
  │ APIs (REST)│        │   Integration   │        │   Service  │
  │            │        │    Services     │        │            │
  │ • OANDA    │        │                 │        │ • EA-based │
  │ • IBKR     │        │ ┌─────────────┐ │        │ • MetaApi  │
  │ • Alpaca   │        │ │ MT5 Terminal│ │        │ • Custom   │
  │ • TradingV │        │ │ Instances   │ │        │            │
  └────────────┘        │ └─────────────┘ │        └────────────┘
                        └─────────────────┘
```

### Revised Trading Connector Architecture (MT4/MT5 First)

This section has been moved to the "Updated Integration Strategy" section above to reflect the new MT4/MT5 first priority approach. Please refer to the detailed implementation examples in that section.
    
    // Check for copy trade commands
    CheckPendingCommands();
}

void SendAccountUpdate() {
    string json = CreateAccountDataJSON();
    string response = SendHTTPRequest(BackendURL + "/mt4/account-data", json);
}
```

**Option B: Third-Party APIs**
- **MetaApi**: Cloud-based MT4/MT5 API ($30/month)
- **CPlugin WebAPI**: Manager API bridge solution
- **MTAPI**: .NET library for direct server connection

## Database Design

### Updated Entity Relationship Diagram

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│      users      │    │ trading_accounts│    │copy_relationships│
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ id (PK)         │    │ id (PK)         │    │ id (PK)         │
│ email           │    │ user_id (FK)    │    │ follower_acc_id │
│ password_hash   │    │ broker_name     │    │ provider_acc_id │
│ first_name      │    │ account_number  │    │ is_active       │
│ last_name       │    │ server_name     │    │ risk_percentage │
│ created_at      │    │ connection_type │    │ max_lot_size    │
│ is_active       │    │ credentials     │    │ symbol_filter   │
└─────────────────┘    │ is_provider     │    │ created_at      │
                       │ is_follower     │    └─────────────────┘
                       │ provider_config │
                       │ follower_config │    ┌─────────────────┐
                       │ status          │    │   trade_signals │
                       │ created_at      │    ├─────────────────┤
                       └─────────────────┘    │ id (PK)         │
                                             │ provider_acc_id │
                                             │ signal_type     │
                                             │ symbol          │
                                             │ action          │
                                             │ volume          │
                                             │ price           │
                                             │ stop_loss       │
                                             │ take_profit     │
                                             │ timestamp       │
                                             │ processed       │
                                             └─────────────────┘
```

### Updated Table Schemas

#### Users Table (Simplified)
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP,
    email_verified BOOLEAN DEFAULT false
);
```

#### Trading Accounts Table (Enhanced)
```sql
CREATE TABLE trading_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    account_number VARCHAR(50) NOT NULL,
    broker_name VARCHAR(100) NOT NULL,
    server_name VARCHAR(100),
    connection_type ENUM('broker_api', 'mt5_python', 'mt4_bridge', 'meta_api') NOT NULL,
    encrypted_credentials JSONB NOT NULL,
    
    -- Role capabilities
    is_provider_enabled BOOLEAN DEFAULT false,
    is_follower_enabled BOOLEAN DEFAULT false,
    
    -- Provider configuration
    provider_config JSONB DEFAULT '{}',  -- signal settings, commissions, etc.
    
    -- Follower configuration  
    follower_config JSONB DEFAULT '{}',  -- default risk settings, filters, etc.
    
    -- Status and metadata
    status ENUM('connecting', 'connected', 'disconnected', 'error') DEFAULT 'connecting',
    last_sync TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(user_id, account_number, broker_name)
);
```

#### Copy Relationships Table (New)
```sql
CREATE TABLE copy_relationships (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    follower_account_id UUID REFERENCES trading_accounts(id) ON DELETE CASCADE,
    provider_account_id UUID REFERENCES trading_accounts(id) ON DELETE CASCADE,
    
    -- Relationship settings
    is_active BOOLEAN DEFAULT true,
    risk_percentage DECIMAL(5,2) DEFAULT 10.00,  -- % of balance to risk
    position_size_mode ENUM('fixed', 'proportional', 'percentage') DEFAULT 'proportional',
    max_lot_size DECIMAL(10,2),
    min_lot_size DECIMAL(10,2),
    
    -- Filtering options
    symbol_filter JSONB DEFAULT '[]',  -- allowed symbols
    symbol_blacklist JSONB DEFAULT '[]',  -- blocked symbols
    max_spread DECIMAL(5,2),
    trading_hours JSONB,  -- time-based filtering
    
    -- Risk management
    daily_loss_limit DECIMAL(15,2),
    max_open_positions INTEGER DEFAULT 10,
    max_drawdown_percent DECIMAL(5,2),
    
    -- Performance tracking
    total_copied_trades INTEGER DEFAULT 0,
    total_profit_loss DECIMAL(15,2) DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(follower_account_id, provider_account_id)
);
```

#### Trade Signals Table (Enhanced)
```sql
CREATE TABLE trade_signals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    provider_account_id UUID REFERENCES trading_accounts(id) ON DELETE CASCADE,
    
    -- Signal details
    signal_type ENUM('open', 'close', 'modify') NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    action ENUM('buy', 'sell') NOT NULL,
    volume DECIMAL(10,2) NOT NULL,
    entry_price DECIMAL(15,5),
    stop_loss DECIMAL(15,5),
    take_profit DECIMAL(15,5),
    
    -- Original trade info
    original_ticket BIGINT,
    original_comment TEXT,
    magic_number BIGINT,
    
    -- Signal metadata
    signal_strength DECIMAL(3,2) DEFAULT 1.0,  -- confidence level
    expected_duration INTEGER,  -- estimated hold time in minutes
    
    -- Processing status
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP,
    broadcast_count INTEGER DEFAULT 0,
    successful_copies INTEGER DEFAULT 0,
    
    INDEX(provider_account_id, created_at),
    INDEX(symbol, created_at),
    INDEX(processed_at)
);
```

#### Copy Trades Table (New)
```sql
CREATE TABLE copy_trades (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    signal_id UUID REFERENCES trade_signals(id) ON DELETE CASCADE,
    copy_relationship_id UUID REFERENCES copy_relationships(id) ON DELETE CASCADE,
    follower_account_id UUID REFERENCES trading_accounts(id) ON DELETE CASCADE,
    
    -- Execution details
    status ENUM('pending', 'executed', 'failed', 'rejected') DEFAULT 'pending',
    execution_price DECIMAL(15,5),
    execution_volume DECIMAL(10,2),
    execution_time TIMESTAMP,
    
    -- Risk adjustments applied
    original_volume DECIMAL(10,2),
    risk_adjusted_volume DECIMAL(10,2),
    position_sizing_mode VARCHAR(20),
    
    -- Results
    ticket BIGINT,  -- broker ticket number
    profit_loss DECIMAL(15,2),
    commission DECIMAL(15,2),
    swap DECIMAL(15,2),
    
    -- Error handling
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX(follower_account_id, created_at),
    INDEX(signal_id, status),
    INDEX(copy_relationship_id, created_at)
);
```

## API Design

### Authentication Endpoints

#### POST /api/auth/register
```json
{
  "request": {
    "email": "<EMAIL>",
    "password": "securePassword123",
    "first_name": "John",
    "last_name": "Doe"
  },
  "response": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe"
    },
    "tokens": {
      "access_token": "jwt_token",
      "refresh_token": "refresh_token",
      "token_type": "bearer"
    }
  }
}
```

### Trading Account Endpoints

#### POST /api/accounts
```json
{
  "request": {
    "account_number": "********",
    "broker_name": "OANDA",
    "connection_type": "broker_api",
    "credentials": {
      "api_key": "encrypted_api_key",
      "account_id": "account_id"
    },
    "is_provider_enabled": true,
    "is_follower_enabled": true
  },
  "response": {
    "success": true,
    "data": {
      "id": "uuid",
      "account_number": "********",
      "broker_name": "OANDA",
      "status": "connecting",
      "connection_type": "broker_api"
    }
  }
}
```

#### GET /api/accounts
```json
{
  "response": {
    "success": true,
    "data": [
      {
        "id": "uuid",
        "account_number": "********",
        "broker_name": "OANDA",
        "status": "connected",
        "is_provider_enabled": true,
        "is_follower_enabled": true,
        "balance": 10000.00,
        "equity": 10150.00,
        "last_sync": "2024-01-15T10:30:00Z"
      }
    ]
  }
}
```

### Copy Trading Endpoints

#### POST /api/copy-relationships
```json
{
  "request": {
    "follower_account_id": "uuid",
    "provider_account_id": "uuid",
    "risk_percentage": 5.0,
    "position_size_mode": "proportional",
    "max_lot_size": 1.0,
    "symbol_filter": ["EURUSD", "GBPUSD"],
    "daily_loss_limit": 100.00
  },
  "response": {
    "success": true,
    "data": {
      "id": "uuid",
      "status": "active",
      "created_at": "2024-01-15T10:30:00Z"
    }
  }
}
```

#### GET /api/copy-relationships
```json
{
  "response": {
    "success": true,
    "data": [
      {
        "id": "uuid",
        "follower_account": {
          "id": "uuid",
          "account_number": "********",
          "broker_name": "Interactive Brokers"
        },
        "provider_account": {
          "id": "uuid", 
          "account_number": "********",
          "broker_name": "OANDA"
        },
        "is_active": true,
        "risk_percentage": 5.0,
        "total_copied_trades": 45,
        "total_profit_loss": 234.50,
        "created_at": "2024-01-15T10:30:00Z"
      }
    ]
  }
}
```

## MT4/MT5 Integration Details

### MT5 Python Integration Service

```python
# mt5_integration_service.py
import MetaTrader5 as mt5
from typing import List, Optional
import asyncio
from dataclasses import dataclass

@dataclass
class MT5Account:
    account: int
    password: str
    server: str
    is_provider: bool = False
    is_follower: bool = False

class MT5IntegrationService:
    def __init__(self):
        self.connected_accounts: Dict[str, MT5Account] = {}
        self.signal_callbacks: List[Callable] = []
        
    async def connect_account(self, account_config: MT5Account) -> bool:
        """Connect to MT5 account using official Python integration"""
        try:
            if not mt5.initialize():
                return False
                
            if not mt5.login(account_config.account, 
                           account_config.password, 
                           account_config.server):
                return False
                
            account_key = f"{account_config.account}@{account_config.server}"
            self.connected_accounts[account_key] = account_config
            
            # Start monitoring if provider
            if account_config.is_provider:
                asyncio.create_task(self._monitor_for_signals(account_key))
                
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect MT5 account: {e}")
            return False
    
    async def _monitor_for_signals(self, account_key: str):
        """Monitor MT5 account for new trades to broadcast as signals"""
        last_deal_time = datetime.now()
        
        while account_key in self.connected_accounts:
            try:
                # Get recent deals
                deals = mt5.history_deals_get(last_deal_time, datetime.now())
                
                for deal in deals:
                    if deal.time > last_deal_time.timestamp():
                        signal = self._convert_deal_to_signal(deal)
                        await self._broadcast_signal(signal)
                        last_deal_time = datetime.fromtimestamp(deal.time)
                        
                await asyncio.sleep(1)  # Check every second
                
            except Exception as e:
                logger.error(f"Error monitoring MT5 signals: {e}")
                await asyncio.sleep(5)
    
    async def execute_copy_trade(self, account_key: str, signal: TradeSignal) -> bool:
        """Execute copy trade on MT5 account"""
        try:
            request = self._convert_signal_to_request(signal)
            result = mt5.order_send(request)
            
            return result.retcode == mt5.TRADE_RETCODE_DONE
            
        except Exception as e:
            logger.error(f"Failed to execute copy trade: {e}")
            return False
```

### MT4 Bridge EA Implementation

```mql4
// TradeSync_MT4_Bridge.mq4
#property version   "1.00"

extern string BackendURL = "https://api.tradesync.com";
extern string ApiKey = "";
extern string AccountUUID = "";
extern int DataUpdateInterval = 5;     // seconds
extern int CommandCheckInterval = 2;   // seconds
extern bool EnableProvider = true;
extern bool EnableFollower = true;

// Global variables
datetime lastDataUpdate = 0;
datetime lastCommandCheck = 0;
datetime lastTradeTime = 0;
string headers;

int OnInit() {
    headers = "Content-Type: application/json\r\nAuthorization: Bearer " + ApiKey;
    Print("TradeSync Bridge EA started for account: " + AccountUUID);
    EventSetTimer(1);
    return(INIT_SUCCEEDED);
}

void OnTimer() {
    // Send account data updates
    if (TimeCurrent() - lastDataUpdate >= DataUpdateInterval) {
        SendAccountUpdate();
        lastDataUpdate = TimeCurrent();
    }
    
    // Check for copy trade commands
    if (EnableFollower && TimeCurrent() - lastCommandCheck >= CommandCheckInterval) {
        CheckCopyTradeCommands();
        lastCommandCheck = TimeCurrent();
    }
}

void OnTrade() {
    // Send trade signals when provider mode enabled
    if (EnableProvider) {
        SendTradeSignal();
    }
}

void SendAccountUpdate() {
    string json = CreateAccountDataJSON();
    string url = BackendURL + "/api/mt4/accounts/" + AccountUUID + "/data";
    
    int response = SendHTTPRequest(url, json, headers);
    if (response != 200) {
        Print("Failed to send account update, response: " + IntegerToString(response));
    }
}

void SendTradeSignal() {
    // Get the most recent trade
    int total = OrdersHistoryTotal();
    if (total > 0 && OrderSelect(total - 1, SELECT_BY_POS, MODE_HISTORY)) {
        if (OrderCloseTime() > lastTradeTime) {
            string signal = CreateTradeSignalJSON();
            string url = BackendURL + "/api/signals";
            
            int response = SendHTTPRequest(url, signal, headers);
            lastTradeTime = OrderCloseTime();
        }
    }
}

void CheckCopyTradeCommands() {
    string url = BackendURL + "/api/mt4/accounts/" + AccountUUID + "/commands";
    string response = SendHTTPGetRequest(url, headers);
    
    if (StringLen(response) > 0) {
        ProcessCopyTradeCommands(response);
    }
}

string CreateTradeSignalJSON() {
    string json = "{";
    json += "\"account_id\":\"" + AccountUUID + "\",";
    json += "\"signal_type\":\"" + (OrderType() <= 1 ? "open" : "pending") + "\",";
    json += "\"symbol\":\"" + OrderSymbol() + "\",";
    json += "\"action\":\"" + (OrderType() % 2 == 0 ? "buy" : "sell") + "\",";
    json += "\"volume\":" + DoubleToString(OrderLots(), 2) + ",";
    json += "\"entry_price\":" + DoubleToString(OrderOpenPrice(), 5) + ",";
    json += "\"stop_loss\":" + DoubleToString(OrderStopLoss(), 5) + ",";
    json += "\"take_profit\":" + DoubleToString(OrderTakeProfit(), 5) + ",";
    json += "\"timestamp\":\"" + TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS) + "\"";
    json += "}";
    return json;
}
```

## Copy Trading Engine Implementation

```python
# copy_trading_engine.py
import asyncio
from typing import List, Dict
from dataclasses import dataclass
from sqlalchemy.orm import Session

class CopyTradingEngine:
    def __init__(self, db_session: Session, redis_client):
        self.db = db_session
        self.redis = redis_client
        self.active_relationships: Dict[str, CopyRelationship] = {}
        self.connector_factory = TradingConnectorFactory()
        
    async def start(self):
        """Start the copy trading engine"""
        await self._load_active_relationships()
        await self._start_signal_processing()
        
    async def _load_active_relationships(self):
        """Load all active copy relationships from database"""
        relationships = self.db.query(CopyRelationship).filter(
            CopyRelationship.is_active == True
        ).all()
        
        for rel in relationships:
            await self._initialize_relationship(rel)
    
    async def process_trade_signal(self, signal: TradeSignal):
        """Process incoming trade signal and copy to followers"""
        try:
            # Find all copy relationships for this provider
            relationships = await self._get_relationships_for_provider(
                signal.provider_account_id
            )
            
            copy_tasks = []
            for relationship in relationships:
                if await self._should_copy_signal(signal, relationship):
                    copy_tasks.append(
                        self._execute_copy_trade(signal, relationship)
                    )
            
            # Execute all copy trades concurrently
            if copy_tasks:
                results = await asyncio.gather(*copy_tasks, return_exceptions=True)
                await self._log_copy_results(signal, relationships, results)
                
        except Exception as e:
            logger.error(f"Error processing trade signal {signal.id}: {e}")
    
    async def _should_copy_signal(self, signal: TradeSignal, 
                                  relationship: CopyRelationship) -> bool:
        """Determine if signal should be copied based on filters and risk rules"""
        
        # Check symbol filter
        if (relationship.symbol_filter and 
            signal.symbol not in relationship.symbol_filter):
            return False
            
        # Check symbol blacklist
        if signal.symbol in relationship.symbol_blacklist:
            return False
            
        # Check daily loss limit
        daily_pl = await self._get_daily_pnl(relationship.id)
        if (relationship.daily_loss_limit and 
            daily_pl < -relationship.daily_loss_limit):
            return False
            
        # Check maximum open positions
        open_positions = await self._count_open_positions(
            relationship.follower_account_id
        )
        if open_positions >= relationship.max_open_positions:
            return False
            
        # Check trading hours
        if not await self._is_trading_time_allowed(relationship):
            return False
            
        return True
    
    async def _execute_copy_trade(self, signal: TradeSignal, 
                                  relationship: CopyRelationship) -> CopyTradeResult:
        """Execute the actual copy trade"""
        try:
            # Get connector for follower account
            connector = await self._get_account_connector(
                relationship.follower_account_id
            )
            
            # Calculate position size based on risk settings
            adjusted_volume = await self._calculate_position_size(
                signal, relationship
            )
            
            # Create copy trade order
            copy_order = Order(
                symbol=signal.symbol,
                action=signal.action,
                volume=adjusted_volume,
                order_type=signal.signal_type,
                stop_loss=signal.stop_loss,
                take_profit=signal.take_profit
            )
            
            # Execute the trade
            result = await connector.place_order(copy_order)
            
            # Record copy trade in database
            copy_trade = CopyTrade(
                signal_id=signal.id,
                copy_relationship_id=relationship.id,
                follower_account_id=relationship.follower_account_id,
                status='executed' if result.success else 'failed',
                execution_price=result.price,
                execution_volume=adjusted_volume,
                original_volume=signal.volume,
                ticket=result.ticket,
                error_message=result.error_message
            )
            
            self.db.add(copy_trade)
            await self.db.commit()
            
            return CopyTradeResult(success=result.success, copy_trade=copy_trade)
            
        except Exception as e:
            logger.error(f"Failed to execute copy trade: {e}")
            return CopyTradeResult(success=False, error=str(e))
    
    async def _calculate_position_size(self, signal: TradeSignal, 
                                       relationship: CopyRelationship) -> float:
        """Calculate adjusted position size based on risk settings"""
        follower_account = await self._get_account_data(
            relationship.follower_account_id
        )
        
        if relationship.position_size_mode == 'fixed':
            # Use fixed lot size
            return min(relationship.max_lot_size or signal.volume, signal.volume)
            
        elif relationship.position_size_mode == 'proportional':
            # Calculate proportional to account balance
            provider_account = await self._get_account_data(signal.provider_account_id)
            
            if provider_account.balance > 0:
                ratio = follower_account.balance / provider_account.balance
                adjusted_volume = signal.volume * ratio
                
                # Apply risk percentage
                risk_factor = relationship.risk_percentage / 100.0
                adjusted_volume *= risk_factor
                
                # Apply min/max limits
                if relationship.max_lot_size:
                    adjusted_volume = min(adjusted_volume, relationship.max_lot_size)
                if relationship.min_lot_size:
                    adjusted_volume = max(adjusted_volume, relationship.min_lot_size)
                    
                return adjusted_volume
                
        elif relationship.position_size_mode == 'percentage':
            # Use percentage of account balance
            risk_amount = follower_account.balance * (relationship.risk_percentage / 100.0)
            
            # Convert risk amount to lot size (simplified calculation)
            # This would need proper position sizing based on symbol specifications
            lot_value = 100000  # Standard lot for major forex pairs
            pip_value = 10  # USD per pip for standard lot
            stop_distance = abs(signal.entry_price - signal.stop_loss) if signal.stop_loss else 50
            
            if stop_distance > 0:
                adjusted_volume = risk_amount / (stop_distance * pip_value)
                return min(adjusted_volume, relationship.max_lot_size or 1.0)
        
        return signal.volume  # Fallback to original volume
```

## Updated Integration Strategy (Revised Priority)

### **Priority 1: MT4/MT5 Integration (Primary Focus)**

The platform will primarily focus on MetaTrader 4 and MetaTrader 5 integration as the core trading platform support, covering the majority of retail forex and CFD trading.

#### **Phase 1A: MT5 Integration (Official Python Support)**

**MetaTrader5 Python Integration:**
```python
import MetaTrader5 as mt5
from typing import Dict, List, Optional
import asyncio
from dataclasses import dataclass

@dataclass
class AccountConnection:
    account: int
    password: str
    server: str
    status: str

class MT5IntegrationService:
    def __init__(self):
        self.connected_accounts: Dict[str, AccountConnection] = {}
    
    async def connect_account(self, account_id: str, account: int, 
                            password: str, server: str) -> bool:
        """Connect to MT5 account using official Python package"""
        if not mt5.initialize():
            raise Exception("Failed to initialize MT5")
        
        if mt5.login(account, password, server):
            self.connected_accounts[account_id] = AccountConnection(
                account=account, password=password, 
                server=server, status="connected"
            )
            return True
        return False
    
    async def get_account_info(self, account_id: str) -> Dict:
        """Get account information and balance"""
        if account_id not in self.connected_accounts:
            raise Exception("Account not connected")
        
        account_info = mt5.account_info()
        return {
            "balance": account_info.balance,
            "equity": account_info.equity,
            "margin": account_info.margin,
            "free_margin": account_info.margin_free,
            "margin_level": account_info.margin_level
        }
    
    async def place_order(self, account_id: str, request: Dict) -> Dict:
        """Execute trade order on MT5 account"""
        mt5_request = {
            "action": request["action"],
            "symbol": request["symbol"],
            "volume": request["volume"],
            "type": request["order_type"],
            "price": request.get("price", 0),
            "sl": request.get("stop_loss", 0),
            "tp": request.get("take_profit", 0),
            "comment": request.get("comment", "TradeSync Copy")
        }
        
        result = mt5.order_send(mt5_request)
        return {
            "order_id": result.order,
            "retcode": result.retcode,
            "comment": result.comment
        }
```

#### **Phase 1B: MT4 Integration (EA Bridge Solution)**

**Custom Expert Advisor Bridge:**
```mql4
// TradeSync_MT4_Bridge.mq4
extern string BackendURL = "https://api.tradesync.com";
extern string ApiKey = "";
extern string AccountId = "";
extern bool EnableProvider = true;
extern bool EnableFollower = true;
extern int TimerInterval = 1000; // 1 second

int OnInit() {
    EventSetTimer(TimerInterval);
    SendAccountRegistration();
    return INIT_SUCCEEDED;
}

void OnDeinit(const int reason) {
    EventKillTimer();
}

void OnTrade() {
    if (EnableProvider) {
        SendTradeSignal();
    }
}

void OnTimer() {
    SendHeartbeat();
    SendAccountUpdate();
    if (EnableFollower) {
        CheckCopyTradeCommands();
    }
}

void SendTradeSignal() {
    string json = CreateTradeSignalJson();
    SendHttpRequest(BackendURL + "/api/signals", json);
}

void CheckCopyTradeCommands() {
    string response = SendHttpRequest(BackendURL + "/api/commands/" + AccountId, "");
    ProcessCopyCommands(response);
}
```

**Backend MT4 Bridge Service:**
```python
from fastapi import FastAPI, HTTPException
from typing import Dict, List
import httpx
import asyncio

class MT4BridgeService:
    def __init__(self):
        self.connected_accounts: Dict[str, Dict] = {}
        self.pending_commands: Dict[str, List] = {}
    
    async def register_account(self, account_data: Dict):
        """Register MT4 account via EA bridge"""
        account_id = account_data["account_id"]
        self.connected_accounts[account_id] = {
            "account_number": account_data["account_number"],
            "broker": account_data["broker"],
            "last_heartbeat": asyncio.get_event_loop().time(),
            "status": "connected"
        }
    
    async def receive_trade_signal(self, signal_data: Dict):
        """Process trade signal from MT4 EA"""
        signal = {
            "provider_account": signal_data["account_id"],
            "symbol": signal_data["symbol"],
            "action": signal_data["action"],
            "volume": signal_data["volume"],
            "price": signal_data["price"],
            "timestamp": signal_data["timestamp"]
        }
        
        # Broadcast to followers
        await self.broadcast_to_followers(signal)
    
    async def send_copy_command(self, follower_account: str, command: Dict):
        """Queue copy trade command for MT4 EA"""
        if follower_account not in self.pending_commands:
            self.pending_commands[follower_account] = []
        
        self.pending_commands[follower_account].append(command)
    
    async def get_pending_commands(self, account_id: str) -> List[Dict]:
        """Return pending commands for MT4 EA"""
        commands = self.pending_commands.get(account_id, [])
        self.pending_commands[account_id] = []  # Clear after retrieval
        return commands
```

### **Priority 2: Additional Trading Platforms (Phase 2)**

#### **cTrader Integration**
```csharp
// cTrader cBot for TradeSync integration
using cAlgo.API;
using System.Net.Http;
using System.Text;
using Newtonsoft.Json;

[Robot(TimeZone = TimeZones.UTC)]
public class TradeSyncBot : Robot
{
    [Parameter("API URL", DefaultValue = "https://api.tradesync.com")]
    public string ApiUrl { get; set; }
    
    [Parameter("API Key")]
    public string ApiKey { get; set; }
    
    private HttpClient httpClient;
    
    protected override void OnStart()
    {
        httpClient = new HttpClient();
        Timer.Start(1); // 1 second timer
    }
    
    protected override void OnTrade(TradeResult result)
    {
        SendTradeSignal(result);
    }
    
    protected override void OnTimer()
    {
        CheckCopyCommands();
    }
}
```

#### **Crypto Exchange Integration**
```python
class CryptoExchangeService:
    def __init__(self):
        self.binance_client = None
        self.bybit_client = None
    
    async def connect_binance(self, api_key: str, secret: str):
        """Connect to Binance API"""
        # Implementation for Binance integration
        pass
    
    async def connect_bybit(self, api_key: str, secret: str):
        """Connect to Bybit API"""
        # Implementation for Bybit integration
        pass
```

### **Priority 3: Traditional Broker APIs (Phase 3)**

**Unified Broker Interface:**
```python
from abc import ABC, abstractmethod

class ************************(ABC):
    @abstractmethod
    async def connect(self, credentials: Dict) -> bool:
        pass
    
    @abstractmethod
    async def get_account_info(self) -> Dict:
        pass
    
    @abstractmethod
    async def place_order(self, order: Dict) -> Dict:
        pass
    
    @abstractmethod
    async def get_positions(self) -> List[Dict]:
        pass

class OANDAIntegration(************************):
    """OANDA REST API integration - Phase 3"""
    async def connect(self, credentials: Dict) -> bool:
        # Future implementation
        pass

class InteractiveBrokersIntegration(************************):
    """Interactive Brokers TWS API integration - Phase 3"""
    async def connect(self, credentials: Dict) -> bool:
        # Future implementation
        pass
```

This updated technical design document now reflects:

1. **Clarified user requirements** - single user login with multiple trading accounts
2. **Updated MT4/MT5 integration strategy** based on current API research
3. **Priority-based approach** - MT4/MT5 integration first, then additional trading platforms, then traditional broker APIs
4. **Enhanced database schema** for multi-account copy relationships
5. **Detailed implementation examples** for the copy trading engine

The architecture now properly supports the user's vision of a flexible multi-account platform where each account can be both a signal provider and follower simultaneously. 