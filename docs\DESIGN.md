# TradeSync - Technical Design Document

## Overview

This document provides detailed technical specifications for the TradeSync multi-account social trading platform, including system architecture, API design, database schemas, and implementation details for users managing multiple trading accounts with dual provider/follower capabilities.

## System Architecture

### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Frontend  │    │   Mobile App    │    │  Desktop App    │
│    (React)      │    │   (Optional)    │    │   (Optional)    │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                     ┌───────────▼───────────┐
                     │   Load Balancer       │
                     │   (nginx/AWS ALB)     │
                     └───────────┬───────────┘
                                 │
                ┌────────────────┼────────────────┐
                │                │                │
    ┌───────────▼───────────┐   │    ┌───────────▼───────────┐
    │   Backend API Server  │   │    │   WebSocket Server    │
    │   (Python/FastAPI)    │   │    │   (FastAPI/WebSocket) │
    └───────────┬───────────┘   │    └───────────┬───────────┘
                │               │                │
                └───────────────┼────────────────┘
                                │
                ┌───────────────▼───────────────┐
                │        Message Queue          │
                │     (Redis Pub/Sub)          │
                └───────────────┬───────────────┘
                                │
        ┌───────────────────────┼───────────────────────┐
        │                       │                       │
┌───────▼───────┐      ┌────────▼────────┐      ┌───────▼───────┐
│   PostgreSQL   │      │  Container       │      │    Redis      │
│   Database     │      │  Orchestrator    │      │    Cache      │
│                │      │  (Kubernetes)    │      │               │
└────────────────┘      └────────┬────────┘      └───────────────┘
                                 │
                    ┌────────────┼────────────┐
                    │            │            │
            ┌───────▼───────┐   │    ┌───────▼───────┐
            │ MT4 Container │   │    │ MT5 Container │
            │ (Multi-User)  │   │    │ (Multi-User)  │
            │               │   │    │               │
            │ ┌─────────────┐ │   │    │ ┌─────────────┐ │
            │ │Multi-Account│ │   │    │ │Multi-Account│ │
            │ │     EA      │ │   │    │ │     EA      │ │
            │ │ ┌─────────┐ │ │   │    │ │ ┌─────────┐ │ │
            │ │ │User1 Acc│ │ │   │    │ │ │User3 Acc│ │ │
            │ │ │User2 Acc│ │ │   │    │ │ │User4 Acc│ │ │
            │ │ └─────────┘ │ │   │    │ │ └─────────┘ │ │
            │ └─────────────┘ │   │    │ └─────────────┘ │
            └───────────────┘   │    └───────────────┘
                               ...
                    (Multiple users per container)
```

### Component Responsibilities

#### Frontend (React Application)
- **Responsibilities**: User interface, real-time data display, trade execution forms
- **Technologies**: React, TypeScript, Redux Toolkit, Material-UI
- **Communication**: REST APIs for CRUD operations, WebSocket for real-time updates

#### Backend API Server
- **Responsibilities**: Business logic, authentication, data validation, container management
- **Technologies**: Python 3.11+, FastAPI, SQLAlchemy, Pydantic
- **Communication**: REST APIs, database queries, container orchestration APIs

#### WebSocket Server  
- **Responsibilities**: Real-time data streaming, live updates to frontend
- **Technologies**: FastAPI WebSocket or Starlette WebSocket
- **Communication**: Message queue subscription, frontend WebSocket connections

#### Container Orchestrator
- **Responsibilities**: MT4/MT5 container lifecycle management, resource allocation
- **Technologies**: Kubernetes or Docker Swarm
- **Communication**: Container APIs, health monitoring

#### Unified Multi-Role EA
- **Responsibilities**: Dual functionality for signal generation and trade execution per account configuration
- **Technologies**: MQL4/MQL5 with full trading access (master password for all accounts)
- **Communication**: HTTP requests for both signal publishing and command polling
- **Features**: Account-specific role configuration, risk management, performance tracking

#### Copy Trading Engine
- **Responsibilities**: Account-to-account signal processing, copy logic, multi-provider conflict resolution, performance tracking
- **Technologies**: Python FastAPI, SQLAlchemy, Redis for queuing
- **Communication**: Signal ingestion from provider accounts, trade commands to copying accounts

---

## Database Design

### Entity Relationship Diagram

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│      users      │    │ trading_accounts│    │   containers    │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ id (PK)         │    │ id (PK)         │    │ id (PK)         │
│ email           │◄───┤ user_id (FK)    │    │ container_id    │
│ password_hash   │    │ account_number  │    │ broker_name     │
│ first_name      │    │ server_name     │    │ terminal_type   │
│ last_name       │    │ broker_name     │    │ status          │
│ user_type       │    │ account_type    │    │ created_at      │
│ created_at      │    │ access_type     │    │ last_heartbeat  │
│ updated_at      │    │ encrypted_creds │    │ max_accounts    │
│ is_active       │    │ connection_type │    │ current_accounts│
│ last_login      │    │ status          │    └─────────────────┘
└─────────────────┘    │ created_at      │           │
           │            │ updated_at      │           │
           │            │ container_id(FK)│           │
           │            └─────────┬───────┘           │
           │                      └───────────────────┘
           │
    ┌──────┴──────┐
    │             │
┌───▼──────────┐ ┌▼────────────────┐    ┌─────────────────┐
│strategy_provs│ │   followers     │    │copy_relationships│
├──────────────┤ ├─────────────────┤    ├─────────────────┤
│ id (PK)      │ │ id (PK)         │    │ id (PK)         │
│ user_id (FK) │ │ user_id (FK)    │◄───┤ follower_id(FK) │
│ display_name │ │ total_balance   │    │ sp_id (FK)      │───┐
│ description  │ │ risk_tolerance  │    │ copy_ratio      │   │
│ is_public    │ │ max_drawdown    │    │ max_lots        │   │
│ min_balance  │ │ created_at      │    │ status          │   │
│ commission   │ │ updated_at      │    │ created_at      │   │
│ created_at   │ └─────────────────┘    │ updated_at      │   │
│ updated_at   │                        └─────────────────┘   │
└──────────────┘                                              │
       │                                                      │
       └──────────────────────────────────────────────────────┘
                                │
        ┌───────────────────────┼───────────────────────┐
        │                       │                       │
┌───────▼───────┐      ┌────────▼────────┐      ┌───────▼───────┐
│ trade_signals │      │   copy_trades   │      │  sp_performance│
├───────────────┤      ├─────────────────┤      ├───────────────┤
│ id (PK)       │      │ id (PK)         │      │ id (PK)       │
│ sp_id (FK)    │      │ signal_id (FK)  │      │ sp_id (FK)    │
│ symbol        │      │ copy_rel_id(FK) │      │ total_trades  │
│ action        │      │ follower_acc(FK)│      │ win_rate      │
│ lots          │      │ copied_lots     │      │ avg_profit    │
│ price         │      │ status          │      │ max_drawdown  │
│ stop_loss     │      │ executed_at     │      │ sharpe_ratio  │
│ take_profit   │      │ profit_loss     │      │ calculated_at │
│ timestamp     │      │ created_at      │      │ period_start  │
│ processed     │      └─────────────────┘      │ period_end    │
└───────────────┘                               └───────────────┘
```

### Table Schemas

#### Users Table
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP,
    email_verified BOOLEAN DEFAULT false,
    two_factor_enabled BOOLEAN DEFAULT false
);
```

#### Trading Accounts Table
```sql
CREATE TABLE trading_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    account_number VARCHAR(50) NOT NULL,
    server_name VARCHAR(100) NOT NULL,
    broker_name VARCHAR(100),
    account_type ENUM('demo', 'live') DEFAULT 'demo',
    encrypted_credentials TEXT NOT NULL,
    connection_type ENUM('mt4', 'mt5', 'api') NOT NULL,
    container_id UUID REFERENCES containers(id) ON DELETE SET NULL,
    is_provider_enabled BOOLEAN DEFAULT false,
    is_follower_enabled BOOLEAN DEFAULT true,
    status ENUM('connecting', 'connected', 'disconnected', 'error') DEFAULT 'connecting',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_sync TIMESTAMP,
    UNIQUE(user_id, account_number, server_name)
);
```

#### Containers Table
```sql
CREATE TABLE containers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    container_id VARCHAR(255) UNIQUE NOT NULL,
    broker_name VARCHAR(100) NOT NULL,
    terminal_type ENUM('mt4', 'mt5') NOT NULL,
    status ENUM('creating', 'running', 'stopped', 'error') DEFAULT 'creating',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_heartbeat TIMESTAMP,
    max_accounts INTEGER DEFAULT 10,
    current_accounts INTEGER DEFAULT 0,
    resource_limits JSONB,
    environment_config JSONB
);
```

#### Positions Table
```sql
CREATE TABLE positions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID REFERENCES trading_accounts(id) ON DELETE CASCADE,
    ticket BIGINT NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    lots DECIMAL(10,2) NOT NULL,
    open_price DECIMAL(15,5) NOT NULL,
    current_price DECIMAL(15,5),
    profit DECIMAL(15,2) DEFAULT 0,
    swap DECIMAL(15,2) DEFAULT 0,
    commission DECIMAL(15,2) DEFAULT 0,
    open_time TIMESTAMP NOT NULL,
    comment TEXT,
    magic_number BIGINT DEFAULT 0,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(account_id, ticket)
);
```

#### Orders Table
```sql
CREATE TABLE orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID REFERENCES trading_accounts(id) ON DELETE CASCADE,
    ticket BIGINT,
    symbol VARCHAR(20) NOT NULL,
    lots DECIMAL(10,2) NOT NULL,
    order_type ENUM('buy', 'sell', 'buy_limit', 'sell_limit', 'buy_stop', 'sell_stop') NOT NULL,
    open_price DECIMAL(15,5),
    target_price DECIMAL(15,5),
    stop_loss DECIMAL(15,5),
    take_profit DECIMAL(15,5),
    status ENUM('pending', 'filled', 'cancelled', 'rejected') DEFAULT 'pending',
    filled_time TIMESTAMP,
    comment TEXT,
    magic_number BIGINT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(account_id, ticket)
);
```

#### Account Snapshots Table
```sql
CREATE TABLE account_snapshots (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID REFERENCES trading_accounts(id) ON DELETE CASCADE,
    balance DECIMAL(15,2) NOT NULL,
    equity DECIMAL(15,2) NOT NULL,
    margin DECIMAL(15,2) DEFAULT 0,
    margin_free DECIMAL(15,2) DEFAULT 0,
    margin_level DECIMAL(8,2) DEFAULT 0,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX(account_id, timestamp)
);
```

#### Account Provider Settings Table
```sql
CREATE TABLE account_provider_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID REFERENCES trading_accounts(id) ON DELETE CASCADE,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    is_public BOOLEAN DEFAULT true,
    min_follower_balance DECIMAL(15,2) DEFAULT 0,
    commission_rate DECIMAL(5,2) DEFAULT 0, -- Percentage
    max_followers INTEGER DEFAULT 1000,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(account_id)
);
```

#### Account Follower Settings Table
```sql
CREATE TABLE account_follower_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID REFERENCES trading_accounts(id) ON DELETE CASCADE,
    max_daily_loss DECIMAL(15,2) DEFAULT 1000.00,
    max_weekly_loss DECIMAL(15,2) DEFAULT 5000.00,
    max_drawdown_percent DECIMAL(5,2) DEFAULT 20.00,
    default_copy_ratio DECIMAL(5,2) DEFAULT 1.00,
    max_open_positions INTEGER DEFAULT 10,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(account_id)
);
```

#### Copy Relationships Table
```sql
CREATE TABLE copy_relationships (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    follower_account_id UUID REFERENCES trading_accounts(id) ON DELETE CASCADE,
    provider_account_id UUID REFERENCES trading_accounts(id) ON DELETE CASCADE,
    copy_ratio DECIMAL(5,2) DEFAULT 1.00, -- 1.0 = 100%, 0.5 = 50%
    max_lots DECIMAL(10,2) DEFAULT 10.00,
    min_lots DECIMAL(10,2) DEFAULT 0.01,
    symbol_filter TEXT, -- JSON array of allowed symbols
    status ENUM('active', 'paused', 'stopped') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(follower_account_id, provider_account_id)
);
```

#### Trade Signals Table
```sql
CREATE TABLE trade_signals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    provider_account_id UUID REFERENCES trading_accounts(id) ON DELETE CASCADE,
    symbol VARCHAR(20) NOT NULL,
    action ENUM('buy', 'sell', 'close_buy', 'close_sell', 'modify') NOT NULL,
    lots DECIMAL(10,2) NOT NULL,
    price DECIMAL(15,5),
    stop_loss DECIMAL(15,5),
    take_profit DECIMAL(15,5),
    ticket BIGINT, -- Original ticket from provider account
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed BOOLEAN DEFAULT false,
    followers_notified INTEGER DEFAULT 0,
    INDEX(provider_account_id, timestamp),
    INDEX(processed, timestamp)
);
```

#### Copy Trades Table
```sql
CREATE TABLE copy_trades (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    signal_id UUID REFERENCES trade_signals(id) ON DELETE CASCADE,
    copy_relationship_id UUID REFERENCES copy_relationships(id) ON DELETE CASCADE,
    follower_account_id UUID REFERENCES trading_accounts(id) ON DELETE CASCADE,
    provider_account_id UUID REFERENCES trading_accounts(id) ON DELETE CASCADE,
    copied_lots DECIMAL(10,2) NOT NULL,
    original_lots DECIMAL(10,2) NOT NULL,
    copied_price DECIMAL(15,5),
    status ENUM('pending', 'executed', 'failed', 'cancelled') DEFAULT 'pending',
    follower_ticket BIGINT, -- Ticket in follower's account
    provider_ticket BIGINT, -- Original ticket from provider
    executed_at TIMESTAMP,
    profit_loss DECIMAL(15,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX(copy_relationship_id, created_at),
    INDEX(follower_account_id, created_at),
    INDEX(status, created_at)
);
```

#### Account Performance Table
```sql
CREATE TABLE account_performance (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID REFERENCES trading_accounts(id) ON DELETE CASCADE,
    performance_type ENUM('provider', 'follower', 'combined') NOT NULL,
    total_trades INTEGER DEFAULT 0,
    winning_trades INTEGER DEFAULT 0,
    losing_trades INTEGER DEFAULT 0,
    win_rate DECIMAL(5,2) DEFAULT 0,
    avg_profit DECIMAL(15,2) DEFAULT 0,
    avg_loss DECIMAL(15,2) DEFAULT 0,
    max_drawdown DECIMAL(15,2) DEFAULT 0,
    sharpe_ratio DECIMAL(8,4) DEFAULT 0,
    total_followers INTEGER DEFAULT 0, -- For provider performance
    total_copied_from INTEGER DEFAULT 0, -- For follower performance
    calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    period_start TIMESTAMP NOT NULL,
    period_end TIMESTAMP NOT NULL,
    INDEX(account_id, performance_type, period_end),
    INDEX(performance_type, win_rate DESC, sharpe_ratio DESC)
);
```

---

## API Design

### FastAPI Pydantic Models

#### Authentication Models
```python
from pydantic import BaseModel, EmailStr
from typing import Optional
from uuid import UUID

class UserRegisterRequest(BaseModel):
    email: EmailStr
    password: str
    first_name: str
    last_name: str

class UserResponse(BaseModel):
    id: UUID
    email: str
    first_name: str
    last_name: str

class TokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"

class AuthResponse(BaseModel):
    user: UserResponse
    tokens: TokenResponse
```

### Authentication Endpoints

#### POST /api/auth/register
```python
@app.post("/api/auth/register", response_model=AuthResponse)
async def register(user_data: UserRegisterRequest):
    # Implementation here
    pass
```

#### POST /api/auth/login
```python
class LoginRequest(BaseModel):
    email: EmailStr
    password: str

@app.post("/api/auth/login", response_model=AuthResponse)
async def login(credentials: LoginRequest):
    # Implementation here
    # Returns: AuthResponse with user info and tokens
    pass
```

**Example Request:**
```json
{
  "email": "<EMAIL>", 
  "password": "securePassword123"
}
```

**Example Response:**
```json
{
  "user": {
    "id": "uuid",
    "email": "<EMAIL>", 
    "first_name": "John",
    "last_name": "Doe"
  },
  "tokens": {
    "access_token": "jwt_token",
    "refresh_token": "refresh_token",
    "token_type": "bearer"
  }
}
```

### Trading Account Endpoints

#### Pydantic Models for Trading Accounts
```python
from enum import Enum
from typing import Optional

class AccountType(str, Enum):
    DEMO = "demo"
    LIVE = "live"

class ConnectionType(str, Enum):
    MT4 = "mt4"
    MT5 = "mt5"
    API = "api"

class AccessType(str, Enum):
    INVESTOR = "investor"  # Read-only access
    MASTER = "master"     # Full trading access

class TradingAccountCreate(BaseModel):
    account_number: str
    server_name: str
    broker_name: str
    account_type: AccountType
    connection_type: ConnectionType
    access_type: AccessType
    credentials: str  # Encrypted credentials

class TradingAccountResponse(BaseModel):
    id: UUID
    account_number: str
    server_name: str
    broker_name: str
    connection_type: ConnectionType
    access_type: AccessType
    status: str
    created_at: datetime
```

#### POST /api/accounts
```python
@app.post("/api/accounts", response_model=TradingAccountResponse)
async def create_trading_account(account_data: TradingAccountCreate):
    # Implementation here
    pass
```

#### GET /api/accounts
```json
{
  "response": {
    "success": true,
    "data": [
      {
        "id": "uuid",
        "accountNumber": "*********",
        "serverName": "ICMarketsSC-Live01",
        "brokerName": "IC Markets",
        "connectionType": "mt5",
        "status": "connected",
        "lastSync": "2024-01-15T10:30:00Z"
      }
    ]
  }
}
```

#### GET /api/accounts/{id}/summary
```json
{
  "response": {
    "success": true,
    "data": {
      "accountId": "uuid",
      "balance": 10000.00,
      "equity": 10150.00,
      "margin": 500.00,
      "marginFree": 9650.00,
      "marginLevel": 2030.00,
      "openPositions": 3,
      "totalProfit": 150.00,
      "lastUpdate": "2024-01-15T10:30:00Z"
    }
  }
}
```

### Position Management Endpoints

#### GET /api/accounts/{id}/positions
```json
{
  "response": {
    "success": true,
    "data": [
      {
        "id": "uuid",
        "ticket": 123456,
        "symbol": "EURUSD",
        "lots": 0.10,
        "openPrice": 1.0850,
        "currentPrice": 1.0865,
        "profit": 15.00,
        "swap": -0.50,
        "commission": -1.00,
        "openTime": "2024-01-15T09:30:00Z",
        "comment": "Manual trade"
      }
    ]
  }
}
```

#### POST /api/accounts/{id}/positions/{positionId}/close
```json
{
  "request": {
    "lots": 0.05
  },
  "response": {
    "success": true,
    "data": {
      "orderId": "uuid",
      "status": "pending",
      "message": "Close order submitted"
    }
  }
}
```

### Trade Execution Endpoints

#### POST /api/accounts/{id}/orders
```json
{
  "request": {
    "symbol": "EURUSD",
    "orderType": "buy",
    "lots": 0.10,
    "price": 1.0850,
    "stopLoss": 1.0800,
    "takeProfit": 1.0900,
    "comment": "Web platform trade"
  },
  "response": {
    "success": true,
    "data": {
      "orderId": "uuid",
      "ticket": 123457,
      "status": "pending",
      "message": "Order submitted successfully"
    }
  }
}
```

### WebSocket Events

#### Account Data Updates
```json
{
  "event": "account_update",
  "data": {
    "accountId": "uuid",
    "balance": 10000.00,
    "equity": 10150.00,
    "margin": 500.00,
    "marginFree": 9650.00,
    "marginLevel": 2030.00,
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

#### Position Updates
```json
{
  "event": "position_update",
  "data": {
    "accountId": "uuid",
    "position": {
      "id": "uuid",
      "ticket": 123456,
      "symbol": "EURUSD",
      "currentPrice": 1.0865,
      "profit": 15.00,
      "timestamp": "2024-01-15T10:30:00Z"
    }
  }
}
```

---

## Container Architecture

### Docker Image Structure

#### Base Image: Ubuntu + Wine
```dockerfile
FROM ubuntu:20.04

# Install Wine and dependencies
RUN apt-get update && apt-get install -y \
    wine \
    xvfb \
    curl \
    unzip \
    && rm -rf /var/lib/apt/lists/*

# Create wine prefix
RUN winecfg

# Install MT4/MT5 terminals
COPY install_terminal.sh /opt/
RUN /opt/install_terminal.sh

# Copy Expert Advisor
COPY TradeSync.ex4 /opt/mt4/MQL4/Experts/
COPY TradeSync.ex5 /opt/mt5/MQL5/Experts/

# Copy configuration templates
COPY config_template.ini /opt/

# Copy startup script
COPY start_terminal.sh /opt/
RUN chmod +x /opt/start_terminal.sh

EXPOSE 8080
CMD ["/opt/start_terminal.sh"]
```

#### Container Environment Variables
```bash
# Container identification
CONTAINER_ID=uuid
BROKER_NAME=ICMarkets
TERMINAL_TYPE=MT5
MAX_ACCOUNTS=8  # Reduced due to all accounts needing full trading capability

# Backend communication
BACKEND_URL=https://api.tradesync.com
BACKEND_API_KEY=container_api_key

# Multi-account configuration
ACCOUNTS_CONFIG_PATH=/opt/accounts.json
DATA_UPDATE_INTERVAL=5
COMMAND_POLL_INTERVAL=2
SIGNAL_DETECTION_INTERVAL=1  # Faster for provider accounts

# Dual-role support
ENABLE_PROVIDER_MODE=true
ENABLE_FOLLOWER_MODE=true

# Resource limits (increased for dual functionality)
MEMORY_LIMIT=3GB
CPU_LIMIT=1500m
```

### Container Lifecycle Management

#### Container States
1. **Creating**: Container is being instantiated
2. **Starting**: Terminal is launching and connecting
3. **Connected**: Terminal connected to broker, EA active
4. **Disconnected**: Temporary connection loss
5. **Error**: Unrecoverable error state
6. **Stopping**: Container is being shut down
7. **Stopped**: Container terminated

#### Health Check Implementation
```bash
#!/bin/bash
# Container health check script

# Check if terminal process is running
if ! pgrep -f "terminal.exe" > /dev/null; then
    echo "Terminal process not found"
    exit 1
fi

# Check EA heartbeat (last update within 30 seconds)
LAST_HEARTBEAT=$(curl -s http://localhost:8080/health | jq -r '.lastHeartbeat')
CURRENT_TIME=$(date +%s)
HEARTBEAT_TIME=$(date -d "$LAST_HEARTBEAT" +%s)

if [ $((CURRENT_TIME - HEARTBEAT_TIME)) -gt 30 ]; then
    echo "EA heartbeat timeout"
    exit 1
fi

echo "Container healthy"
exit 0
```

---

## Expert Advisor Design

### MQL4/MQL5 Code Structure

#### Main EA Functions
```mql5
// TradeSync_UnifiedMultiRole.mq5
#property version "3.00"

// Input parameters
input string BackendURL = "https://api.tradesync.com";
input string ApiKey = "";
input string ContainerId = "";
input int UpdateInterval = 5; // seconds
input int CommandInterval = 2; // seconds
input int SignalInterval = 1; // seconds for provider signal detection
input int MaxAccounts = 8; // Reduced for dual-role accounts

// Global variables
datetime lastUpdate = 0;
datetime lastCommandCheck = 0;
datetime lastSignalCheck = 0;
string headers = "Content-Type: application/json\r\nAuthorization: Bearer " + ApiKey;
string managedAccounts[]; // Array of account IDs managed by this EA
bool accountProviderEnabled[]; // Provider mode per account
bool accountFollowerEnabled[]; // Follower mode per account
int accountCount = 0;

int OnInit() {
    Print("TradeSync Unified Multi-Role EA initialized");
    LoadManagedAccounts();
    EventSetTimer(1); // 1 second timer
    return INIT_SUCCEEDED;
}

void OnDeinit(const int reason) {
    EventKillTimer();
    Print("TradeSync Unified Multi-Role EA deinitialized");
}

void OnTimer() {
    // Update account data every UpdateInterval seconds
    if (TimeCurrent() - lastUpdate >= UpdateInterval) {
        SendMultiAccountUpdate();
        lastUpdate = TimeCurrent();
    }
    
    // Check for copy trade commands every CommandInterval seconds
    if (TimeCurrent() - lastCommandCheck >= CommandInterval) {
        CheckCopyTradeCommands();
        lastCommandCheck = TimeCurrent();
    }
    
    // Detect and send signals every SignalInterval seconds
    if (TimeCurrent() - lastSignalCheck >= SignalInterval) {
        DetectAndSendSignals();
        lastSignalCheck = TimeCurrent();
    }
}

void LoadManagedAccounts() {
    // Load account configuration with role settings from backend
    string url = BackendURL + "/api/ea/containers/" + ContainerId + "/accounts";
    char result[];
    string resultHeaders;
    
    int res = WebRequest("GET", url, headers, 5000, NULL, result, resultHeaders);
    if (res == 200) {
        ParseAccountsConfigWithRoles(CharArrayToString(result));
    }
}

void DetectAndSendSignals() {
    // Check for new trades in provider-enabled accounts and send signals
    for (int i = 0; i < accountCount; i++) {
        if (accountProviderEnabled[i]) {
            CheckAccountForNewSignals(managedAccounts[i]);
        }
    }
}

void CheckCopyTradeCommands() {
    // Check for copy trade commands for follower-enabled accounts
    for (int i = 0; i < accountCount; i++) {
        if (accountFollowerEnabled[i]) {
            CheckAccountForCopyCommands(managedAccounts[i]);
        }
    }
}
```

#### Account Data Collection
```mql5
void SendAccountUpdate() {
    string jsonData = CreateAccountJson();
    string url = BackendURL + "/api/ea/accounts/" + AccountId + "/update";
    
    char postData[];
    StringToCharArray(jsonData, postData, 0, StringLen(jsonData));
    
    char result[];
    string resultHeaders;
    
    int res = WebRequest("POST", url, headers, 5000, postData, result, resultHeaders);
    
    if (res == 200) {
        Print("Account data sent successfully");
    } else {
        Print("Failed to send account data: ", res);
    }
}

string CreateAccountJson() {
    double balance = AccountBalance();
    double equity = AccountEquity();
    double margin = AccountMargin();
    double freeMargin = AccountFreeMargin();
    double marginLevel = AccountMarginLevel();
    
    string json = "{";
    json += "\"balance\":" + DoubleToString(balance, 2) + ",";
    json += "\"equity\":" + DoubleToString(equity, 2) + ",";
    json += "\"margin\":" + DoubleToString(margin, 2) + ",";
    json += "\"marginFree\":" + DoubleToString(freeMargin, 2) + ",";
    json += "\"marginLevel\":" + DoubleToString(marginLevel, 2) + ",";
    json += "\"timestamp\":\"" + TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS) + "\",";
    json += "\"positions\":[" + GetPositionsJson() + "],";
    json += "\"orders\":[" + GetOrdersJson() + "]";
    json += "}";
    
    return json;
}
```

#### Command Processing
```mql5
void CheckCommands() {
    string url = BackendURL + "/api/ea/accounts/" + AccountId + "/commands";
    
    char result[];
    string resultHeaders;
    
    int res = WebRequest("GET", url, headers, 5000, NULL, result, resultHeaders);
    
    if (res == 200) {
        string response = CharArrayToString(result);
        ProcessCommands(response);
    }
}

void ProcessCommands(string jsonResponse) {
    // Parse JSON and execute commands
    // This would use a JSON parsing library or custom parsing
    
    // Example command processing:
    if (StringFind(jsonResponse, "\"action\":\"buy\"") >= 0) {
        ExecuteBuyOrder(jsonResponse);
    } else if (StringFind(jsonResponse, "\"action\":\"sell\"") >= 0) {
        ExecuteSellOrder(jsonResponse);
    } else if (StringFind(jsonResponse, "\"action\":\"close\"") >= 0) {
        ExecuteCloseOrder(jsonResponse);
    }
}

void ExecuteBuyOrder(string commandJson) {
    // Parse order parameters from JSON
    string symbol = ExtractJsonValue(commandJson, "symbol");
    double lots = StringToDouble(ExtractJsonValue(commandJson, "lots"));
    double price = StringToDouble(ExtractJsonValue(commandJson, "price"));
    double sl = StringToDouble(ExtractJsonValue(commandJson, "stopLoss"));
    double tp = StringToDouble(ExtractJsonValue(commandJson, "takeProfit"));
    
    // Execute order
    int ticket = OrderSend(symbol, OP_BUY, lots, Ask, 3, sl, tp, "TradeSync", 0, 0, clrGreen);
    
    // Send result back to backend
    SendOrderResult(ticket, commandJson);
}
```

---

## Security Architecture

### Encryption Strategy

#### Credential Encryption
```python
# Backend encryption service
import os
import base64
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.backends import default_backend
from dataclasses import dataclass
from typing import str

@dataclass
class EncryptedData:
    encrypted: str
    iv: str
    salt: str
    auth_tag: str

class EncryptionService:
    def __init__(self):
        self.algorithm = algorithms.AES
        self.key_length = 32
        self.backend = default_backend()
    
    def encrypt(self, text: str, master_key: str) -> EncryptedData:
        # Generate random IV and salt
        iv = os.urandom(12)  # 96-bit IV for GCM
        salt = os.urandom(16)
        
        # Derive key using PBKDF2
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=self.key_length,
            salt=salt,
            iterations=100000,
            backend=self.backend
        )
        key = kdf.derive(master_key.encode())
        
        # Encrypt using AES-GCM
        cipher = Cipher(
            self.algorithm(key),
            modes.GCM(iv),
            backend=self.backend
        )
        encryptor = cipher.encryptor()
        encryptor.authenticate_additional_data(salt)
        
        ciphertext = encryptor.update(text.encode()) + encryptor.finalize()
        
        return EncryptedData(
            encrypted=base64.b64encode(ciphertext).decode(),
            iv=base64.b64encode(iv).decode(),
            salt=base64.b64encode(salt).decode(),
            auth_tag=base64.b64encode(encryptor.tag).decode()
        )
    
    def decrypt(self, data: EncryptedData, master_key: str) -> str:
        # Decode from base64
        encrypted = base64.b64decode(data.encrypted)
        iv = base64.b64decode(data.iv)
        salt = base64.b64decode(data.salt)
        auth_tag = base64.b64decode(data.auth_tag)
        
        # Derive key using PBKDF2
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=self.key_length,
            salt=salt,
            iterations=100000,
            backend=self.backend
        )
        key = kdf.derive(master_key.encode())
        
        # Decrypt using AES-GCM
        cipher = Cipher(
            self.algorithm(key),
            modes.GCM(iv, auth_tag),
            backend=self.backend
        )
        decryptor = cipher.decryptor()
        decryptor.authenticate_additional_data(salt)
        
        plaintext = decryptor.update(encrypted) + decryptor.finalize()
        return plaintext.decode()
```

#### JWT Token Strategy
```python
# JWT configuration
from dataclasses import dataclass
from datetime import datetime, timedelta
import jwt
import uuid
from typing import Dict, Any

@dataclass
class JWTConfig:
    access_token_secret: str
    refresh_token_secret: str
    access_token_expiry: int = 900  # 15 minutes in seconds
    refresh_token_expiry: int = 604800  # 7 days in seconds
    issuer: str = "tradesync"
    audience: str = "tradesync-users"

@dataclass
class TokenPair:
    access_token: str
    refresh_token: str

class TokenService:
    def __init__(self, config: JWTConfig):
        self.config = config
    
    def generate_token_pair(self, user_id: str) -> TokenPair:
        now = datetime.utcnow()
        
        # Access Token
        access_payload = {
            "user_id": user_id,
            "type": "access",
            "permissions": ["trade", "view"],
            "iat": now,
            "exp": now + timedelta(seconds=self.config.access_token_expiry),
            "iss": self.config.issuer,
            "aud": self.config.audience
        }
        
        access_token = jwt.encode(
            access_payload,
            self.config.access_token_secret,
            algorithm="HS256"
        )
        
        # Refresh Token
        refresh_payload = {
            "user_id": user_id,
            "type": "refresh", 
            "token_id": str(uuid.uuid4()),
            "iat": now,
            "exp": now + timedelta(seconds=self.config.refresh_token_expiry),
            "iss": self.config.issuer,
            "aud": self.config.audience
        }
        
        refresh_token = jwt.encode(
            refresh_payload,
            self.config.refresh_token_secret,
            algorithm="HS256"
        )
        
        return TokenPair(
            access_token=access_token,
            refresh_token=refresh_token
        )
```

### Container Security

#### Security Policies
```yaml
# Kubernetes SecurityContext
apiVersion: v1
kind: Pod
spec:
  securityContext:
    runAsNonRoot: true
    runAsUser: 1000
    fsGroup: 1000
  containers:
  - name: mt5-terminal
    securityContext:
      allowPrivilegeEscalation: false
      readOnlyRootFilesystem: true
      capabilities:
        drop:
        - ALL
    resources:
      limits:
        memory: "512Mi"
        cpu: "500m"
      requests:
        memory: "256Mi"
        cpu: "250m"
```

#### Network Policies
```yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: mt-container-policy
spec:
  podSelector:
    matchLabels:
      app: mt-terminal
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: backend-api
    ports:
    - protocol: TCP
      port: 8080
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 443  # HTTPS to broker
    - protocol: TCP
      port: 80   # HTTP to backend
```

---

## Performance Considerations

### Caching Strategy

#### Redis Cache Implementation
```python
import asyncio
import json
from typing import Any, List
import aioredis
from dataclasses import asdict

class CacheService:
    def __init__(self, redis_url: str):
        self.redis_url = redis_url
        self.redis = None
    
    async def connect(self):
        self.redis = await aioredis.from_url(self.redis_url)
    
    async def cache_account_data(self, account_id: str, data: Any) -> None:
        """Cache account data for 30 seconds"""
        if self.redis:
            await self.redis.setex(
                f"account:{account_id}", 
                30, 
                json.dumps(asdict(data) if hasattr(data, '__dict__') else data)
            )
    
    async def cache_positions(self, account_id: str, positions: List[Any]) -> None:
        """Cache position data for 10 seconds"""
        if self.redis:
            await self.redis.setex(
                f"positions:{account_id}", 
                10, 
                json.dumps([asdict(p) if hasattr(p, '__dict__') else p for p in positions])
            )
    
    async def cache_user_session(self, user_id: str, session: Any) -> None:
        """Cache user session for 15 minutes"""
        if self.redis:
            await self.redis.setex(
                f"session:{user_id}", 
                900, 
                json.dumps(asdict(session) if hasattr(session, '__dict__') else session)
            )
    
    async def get_cached_data(self, key: str) -> Any:
        """Get cached data by key"""
        if self.redis:
            data = await self.redis.get(key)
            return json.loads(data) if data else None
        return None
```

### Database Optimization

#### Indexing Strategy
```sql
-- Performance indexes
CREATE INDEX idx_trading_accounts_user_id ON trading_accounts(user_id);
CREATE INDEX idx_positions_account_id ON positions(account_id);
CREATE INDEX idx_positions_symbol ON positions(symbol);
CREATE INDEX idx_orders_account_id ON orders(account_id);
CREATE INDEX idx_account_snapshots_account_timestamp ON account_snapshots(account_id, timestamp);

-- Composite indexes for common queries
CREATE INDEX idx_positions_account_symbol ON positions(account_id, symbol);
CREATE INDEX idx_orders_account_status ON orders(account_id, status);
```

#### Query Optimization
```sql
-- Optimized query for dashboard data
SELECT 
    ta.id,
    ta.account_number,
    ta.broker_name,
    ta.status,
    las.balance,
    las.equity,
    las.margin,
    COUNT(p.id) as open_positions,
    COALESCE(SUM(p.profit), 0) as total_profit
FROM trading_accounts ta
LEFT JOIN LATERAL (
    SELECT balance, equity, margin 
    FROM account_snapshots 
    WHERE account_id = ta.id 
    ORDER BY timestamp DESC 
    LIMIT 1
) las ON true
LEFT JOIN positions p ON p.account_id = ta.id
WHERE ta.user_id = $1
GROUP BY ta.id, ta.account_number, ta.broker_name, ta.status, las.balance, las.equity, las.margin;
```

### Scalability Considerations

#### Horizontal Scaling
- **Stateless API servers**: Multiple backend instances behind load balancer
- **Database read replicas**: Read operations distributed across replicas
- **Container distribution**: Spread MT4/MT5 containers across multiple nodes
- **Message queue clustering**: Redis cluster for high availability

#### Vertical Scaling
- **Resource allocation**: Dynamic CPU/memory allocation for containers
- **Database tuning**: Connection pooling, query optimization
- **Caching layers**: Multi-level caching (Redis, in-memory, CDN)

---

**Document Version**: 1.0  
**Last Updated**: [Current Date]  
**Review Status**: Draft  
**Approved By**: [Technical Lead] 